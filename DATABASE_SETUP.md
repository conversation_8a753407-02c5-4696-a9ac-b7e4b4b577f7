# Database Setup Guide

## Prerequisites

- PostgreSQL installed locally or access to a PostgreSQL database
- Database connection details

## Setup Steps

### 1. Create Database

```sql
CREATE DATABASE gym_scheduler;
```

### 2. Set Environment Variables

Update `apps/app/.env.local` with your database credentials:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/gym_scheduler
BETTER_AUTH_SECRET=your-secret-key-here-change-in-production
BETTER_AUTH_URL=http://localhost:3000/api/auth
NEXT_PUBLIC_BETTER_AUTH_URL=http://localhost:3000/api/auth
```

### 3. Generate and Run Migrations

```bash
# Generate migration files
pnpm --filter @workspace/auth generate

# Run migrations
pnpm --filter @workspace/auth migrate
```

### 4. Optional: View Database Schema

```bash
# Open Drizzle Studio to view and manage your database
pnpm --filter @workspace/auth studio
```

## Development Commands

- `pnpm dev` - Start the development server
- `pnpm build` - Build the application
- `pnpm --filter @workspace/auth generate` - Generate new migrations
- `pnpm --filter @workspace/auth migrate` - Run pending migrations
- `pnpm --filter @workspace/auth studio` - Open database studio

## Schema Overview

The database includes the following main tables:

- `trainers` - Trainer accounts and profiles
- `customers` - Customer information and session credits
- `customer_purchases` - Purchase history and session credits
- `sessions` - Training sessions with scheduling info (exported as `trainingSessions`)
- `session_participants` - Many-to-many relationship between sessions and
  customers (exported as `trainingSessionParticipants`)
- Better Auth tables for authentication (`user`, `session`, `account`,
  `verification`)
