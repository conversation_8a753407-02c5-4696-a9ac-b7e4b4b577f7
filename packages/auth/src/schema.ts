import { pgTable, text, timestamp, integer, decimal, boolean, uuid, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Trainers table
export const trainers = pgTable('trainers', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  phone: text('phone'),
  emailVerified: boolean('email_verified').default(false),
  image: text('image'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customers table
export const customers = pgTable('customers', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  name: text('name').notNull(),
  email: text('email'),
  phone: text('phone'),
  sessionCredits: integer('session_credits').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customer purchases table
export const customerPurchases = pgTable('customer_purchases', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id)
    .notNull(),
  sessionsPurchased: integer('sessions_purchased').notNull(),
  amountPaid: decimal('amount_paid', { precision: 10, scale: 2 }).notNull(),
  purchaseDate: timestamp('purchase_date').defaultNow().notNull(),
  paymentStatus: text('payment_status').default('completed').notNull(), // completed, pending, failed
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Training sessions table
export const trainingSessions = pgTable('sessions', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  title: text('title').notNull(),
  description: text('description'),
  startTime: timestamp('start_time').notNull(),
  endTime: timestamp('end_time').notNull(),
  minParticipants: integer('min_participants').default(3).notNull(),
  maxParticipants: integer('max_participants').default(5).notNull(),
  status: text('status').default('scheduled').notNull(), // scheduled, confirmed, completed, cancelled
  location: text('location'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Training session participants table (many-to-many relationship)
export const trainingSessionParticipants = pgTable('session_participants', {
  id: uuid('id').defaultRandom().primaryKey(),
  sessionId: uuid('session_id')
    .references(() => trainingSessions.id)
    .notNull(),
  customerId: uuid('customer_id')
    .references(() => customers.id)
    .notNull(),
  status: text('status').default('enrolled').notNull(), // enrolled, confirmed, cancelled
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),
  confirmedAt: timestamp('confirmed_at'),
  creditDeducted: boolean('credit_deducted').default(false).notNull(),
});

// Better Auth tables
export const user = pgTable('user', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  emailVerified: boolean('email_verified')
    .$defaultFn(() => false)
    .notNull(),
  image: text('image'),
  createdAt: timestamp('created_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  updatedAt: timestamp('updated_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  role: text('role').default('trainer'),
});

export const session = pgTable('session', {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expires_at').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  userId: text('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
});

export const account = pgTable('account', {
  id: text('id').primaryKey(),
  accountId: text('account_id').notNull(),
  providerId: text('provider_id').notNull(),
  userId: text('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
});

export const verification = pgTable('verification', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()),
  updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()),
});

// Relations
export const trainersRelations = relations(trainers, ({ many }) => ({
  customers: many(customers),
  trainingSessions: many(trainingSessions),
}));

export const customersRelations = relations(customers, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [customers.trainerId],
    references: [trainers.id],
  }),
  purchases: many(customerPurchases),
  trainingSessionParticipants: many(trainingSessionParticipants),
}));

export const customerPurchasesRelations = relations(customerPurchases, ({ one }) => ({
  customer: one(customers, {
    fields: [customerPurchases.customerId],
    references: [customers.id],
  }),
}));

export const trainingSessionsRelations = relations(trainingSessions, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [trainingSessions.trainerId],
    references: [trainers.id],
  }),
  participants: many(trainingSessionParticipants),
}));

export const trainingSessionParticipantsRelations = relations(trainingSessionParticipants, ({ one }) => ({
  trainingSession: one(trainingSessions, {
    fields: [trainingSessionParticipants.sessionId],
    references: [trainingSessions.id],
  }),
  customer: one(customers, {
    fields: [trainingSessionParticipants.customerId],
    references: [customers.id],
  }),
}));

export type Trainer = typeof trainers.$inferSelect;
export type NewTrainer = typeof trainers.$inferInsert;
export type Customer = typeof customers.$inferSelect;
export type NewCustomer = typeof customers.$inferInsert;
export type CustomerPurchase = typeof customerPurchases.$inferSelect;
export type NewCustomerPurchase = typeof customerPurchases.$inferInsert;
export type TrainingSession = typeof trainingSessions.$inferSelect;
export type NewTrainingSession = typeof trainingSessions.$inferInsert;
export type TrainingSessionParticipant = typeof trainingSessionParticipants.$inferSelect;
export type NewTrainingSessionParticipant = typeof trainingSessionParticipants.$inferInsert;
